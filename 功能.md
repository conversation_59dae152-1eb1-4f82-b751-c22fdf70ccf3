
# 项目功能分析文档

本文档旨在详细分析该项目的各项功能，包括用户端和管理后台，并阐述其核心业务逻辑。

## 一、 核心业务概念

项目围绕着**数字资产的理财和质押**展开，并结合了**节点购买**、**邀请返佣**和**排行榜**等多种玩法。

- **多种数字资产**：系统支持多种数字资产，如 DOP, JU, FISH, BIRD, NAC, FOMO 等。
- **理财产品**：提供不同期限（如30天、180天、360天）和收益率的理财产品。
- **质押挖矿**：用户可以质押指定的数字资产，获取静态和动态收益。
- **节点**：用户可以购买节点，享受节点分红和返佣。
- **邀请关系**：用户之间可以建立邀请关系，邀请人可以从被邀请人的投资中获得返佣。
- **排行榜**：根据用户的业绩进行排名，并给予奖励。

## 二、 用户端功能 (API)

用户端API是项目的主要功能入口，提供了用户注册、登录、资产管理、理财、质押等一系列功能。

### 1. 登录与认证 (`IndexController`)

- **用户登录 (`/api/login`)**:
    - 通过第三方平台授权码(`code`)进行登录。
    - 登录成功后，系统会检查用户是否存在，如果不存在则会自动创建新用户。
    - 生成并返回一个 `token` 用于后续接口的身份认证。

### 2. 首页 (`HomeController`)

- **首页数据 (`/api/home`)**:
    - **全网理财数量**: 统计所有用户的理财总额。
    - **DOP价格**: 获取DOP代币的当前价格。
    - **累计手续费**: 统计质押业务中产生的所有手续费。
    - **节点池**: 根据节点手续费计算节点分红池的金额。
    - **总动态收益**: 统计所有用户的动态收益总和。

### 3. 理财产品 (`ProductController`)

- **理财产品页面 (`/api/product/info`)**:
    - 展示所有可购买的理财产品列表，包括产品名称、利率、期限等信息。
    - 显示当前用户的ID和全网理财总额。
- **购买理财 (`/api/product/buy`)**:
    - 用户选择理财产品并输入购买金额。
    - 系统会校验购买金额是否有效，并执行购买逻辑。
    - 购买成功后，会扣除用户相应资产并生成理财记录。

### 4. 用户个人中心 (`UserController`)

- **我的理财 (`/api/user/product`)**:
    - 展示用户持有的所有理财产品，包括活期和定期。
    - 统计用户的总资产、总收益等信息。
- **我的资产 (`/api/user/assets`)**:
    - 展示用户持有的所有数字资产及其数量和当前价格。
- **绑定邀请码 (`/api/user/bind-code`)**:
    - 用户可以绑定邀请人的邀请码，建立邀请关系。
- **邀请记录 (`/api/user/invite-records`)**:
    - 展示用户的邀请列表，包括被邀请人的ID、理财金额等。
- **赎回活期/定期 (`/api/user/redeem-current`, `/api/user/redeem-regular`)**:
    - 用户可以赎回已到期的理- **领取返佣/定期收益 (`/api/user/receive`, `/api/user/receive-profit`)**:
    - 用户可以手动领取邀请返佣和定期理财产品的收益。
- **收益/返佣记录 (`/api/user/user-log`, `/api/user/reward-log`)**:
    - 提供详细的收益和返佣流水记录，支持按时间、类型等条件筛选。

### 5. 资产划转 (`TransferController`)

- **划转记录 (`/api/user/transfer`)**:
    - 展示用户的资产划转历史记录。
- **划转 (`/api/user/transfer`)**:
    - 用户可以在不同账户或地址之间划转数字资产。
    - 后端会调用第三方API来执行实际的划转操作，并提供重试机制以确保成功。

### 6. 质押 (`StakeUserController`)

- **可质押资产 (`/api/user/stake-assets`)**:
    - 返回当前系统支持质押的资产列表。
- **节点信息 (`/api/user/node/info/{tokenId}`)**:
    - 显示特定资产的节点信息，包括节点价格、用户是否已购买等。
- **购买节点 (`/api/user/node/buy/{tokenId}`)**:
    - 用户支付相应费用购买节点，成为节点持有者。
- **我的质押 (`/api/user/stake/{tokenId}`)**:
    - 展示用户在特定资产上的质押详情，包括质押数量、静态/动态收益池、可提取金额等。
- **质押/解除质押 (`/api/user/stake`, `/api/user/unstake`)**:
    - 用户可以增加或减少质押的资产数量。
- **提取静态池 (`/api/user/stake/claim-static`)**:
    - 用户可以从静态收益池中提取收益。
- **邀请记录/业绩排行 (`/api/user/stake/invite-records`, `/api/user/stake/rank/{tokenId}`)**:
    - 提供基于质押业务的邀请记录和业绩排行榜。
- **销毁DOP (`/api/user/stake/burn`)**:
    - 用户可以销毁DOP代币以获取贡献值或其他权益。

### 7. 定时任务与内部接口 (`JobController`, `InternalApiController`)

- **定时任务**:
    - `JobController` 提供了一系列用于测试和手动触发定时任务的接口，如发放收益、统计购买数量、计算节点业绩等。这些任务在生产环境中通常由定时调度器自动执行。
- **内部接口**:
    - `InternalApiController` 包含一些内部使用的接口，例如修复数据、更新用户关系等，这些接口通常需要密码验证，不对普通用户开放。

## 三、 管理后台功能 (Admin)

管理后台为系统管理员提供了全面的监控和管理功能，确保系统的稳定运行和业务的正常开展。

### 1. 登录与权限 (`AdminController`)

- **管理员登录 (`/admin/login`)**:
    - 管理员使用账号、密码和谷歌验证码进行登录。
    - 系统实现了登录失败次数限制和账户锁定机制，以增强安全性。
- **权限控制**:
    - 基于角色的权限控制（RBAC），不同角色的管理员拥有不同的操作权限。
    - 通过 `/admin/getRouters` 接口动态生成前端路由，实现菜单的权限控制。

### 2. 用户管理 (`AdminUserController`)

- **用户列表与查询**:
    - 提供强大的用户查询功能，可以根据用户ID、邀请码、等级等多种条件进行筛选。
    - 展示用户的详细信息，包括资产、理财、质押、邀请关系等。
- **用户操作**:
    - **空投**: 向指定用户发放数字资产。
    - **设置等级/额度**: 手动修改用户的等级或理财额度。
    - **导入用户**: 支持通过CSV文件批量导入用户或购买记录。
    - **监控列表**: 可以将特定用户加入监控列表，方便重点观察。

### 3. 业务数据管理

- **理财/质押订单管理**:
    - 查看和管理所有的理财和质押订单，支持按多种条件筛选和排序。
- **划转记录**:
    - 查看所有的资产划转记录。
- **收益/返佣/销毁记录**:
    - 查看和管理系统中的各种流水记录。
- **系统参数配置 (`/admin/user/sys-config`)**:
    - 管理员可以修改系统的一些核心参数，如费率、开关等。

### 4. 系统监控 (`ServerController`)

- **服务器监控 (`/admin/monitor/server`)**:
    - 提供服务器的实时状态监控，包括CPU、内存、磁盘等信息，帮助管理员及时发现和解决问题。

### 5. 系统用户管理 (`SysUserController`)

- **管理员账号管理**:
    - 提供对后台管理员账号的增删改查功能。
    - 支持绑定和解绑谷歌验证码，增强账户安全性。

## 四、 总结

该项目是一个功能相对完善的数字资产理财和质押平台。其核心业务逻辑清晰，功能模块划分合理。

- **用户端**：以理财和质押为核心，结合了邀请、节点、排行等多种玩法，具有较强的可玩性和吸引力。
- **管理后台**：提供了全面的数据监控和管理功能，为系统的稳定运行和业务的精细化运营提供了有力支持。

**建议的后续步骤**:

- **数据库结构分析**: 深入分析数据库表结构，可以更清晰地了解各个功能模块之间的数据关系。
- **代码细节审查**: 对核心业务逻辑的Service层代码进行详细审查，可以更深入地理解其实现细节和潜在风险。
- **前端代码分析**: 结合前端代码，可以更直观地了解用户交互流程和页面展示逻辑。
