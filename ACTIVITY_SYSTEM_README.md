# 活动系统 (Activity System)

## 概述

新的活动系统是一个基于期数的质押和销毁系统，用户可以在每个活动期数中参与一次质押和销毁操作。系统设计为与现有质押系统并行工作，提供额外的活动奖励机制。

## 核心功能

### 1. 活动期数管理
- 每个活动期数有独立的配置（质押数量、销毁数量、时间范围）
- 支持多个活动期数并行或顺序进行
- 活动状态管理：未开始、进行中、已结束

### 2. 用户参与跟踪
- 每个用户在每个期数只能参与一次
- 跟踪质押状态、销毁状态和完成状态
- 记录参与时间戳

### 3. 双重系统集成
- 新活动逻辑执行后，调用原有质押/销毁接口
- 保持与现有系统的兼容性
- 独立的数据记录和状态管理

## 数据库设计

### activity_config 表
```sql
CREATE TABLE activity_config (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    period INT NOT NULL COMMENT '活动期数',
    stake_amount DECIMAL(25,8) NOT NULL COMMENT '质押数量',
    burn_amount DECIMAL(25,8) NOT NULL COMMENT '销毁数量',
    start_time DATETIME NOT NULL COMMENT '开始时间',
    end_time DATETIME NOT NULL COMMENT '结束时间',
    status TINYINT DEFAULT 1 COMMENT '状态：0-未开始，1-进行中，2-已结束',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_period (period)
);
```

### user_activity_record 表
```sql
CREATE TABLE user_activity_record (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL COMMENT '用户ID',
    period INT NOT NULL COMMENT '活动期数',
    stake_status TINYINT DEFAULT 0 COMMENT '质押状态：0-未质押，1-已质押',
    burn_status TINYINT DEFAULT 0 COMMENT '销毁状态：0-未销毁，1-已销毁',
    completed TINYINT DEFAULT 0 COMMENT '是否完成：0-未完成，1-已完成',
    stake_time DATETIME NULL COMMENT '质押时间',
    burn_time DATETIME NULL COMMENT '销毁时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_user_period (user_id, period)
);
```

## API 接口

### 用户接口 (/api/activity/*)

#### 获取当前活动
- **GET** `/api/activity/current`
- 返回当前进行中的活动配置

#### 获取指定期数活动
- **GET** `/api/activity/period/{period}`
- 返回指定期数的活动配置

#### 活动质押
- **POST** `/api/activity/stake`
- 参数：`ActivityStakeForm`
- 执行活动质押操作

#### 活动销毁
- **POST** `/api/activity/burn`
- 参数：`ActivityBurnForm`
- 执行活动销毁操作

#### 获取用户活动状态
- **GET** `/api/activity/user/status`
- 参数：`userId`, `period`
- 返回用户在指定期数的参与状态

#### 获取用户活动记录
- **GET** `/api/activity/user/records`
- 参数：`userId`
- 返回用户所有活动参与记录

#### 获取完成用户列表
- **GET** `/api/activity/completed/{period}`
- 返回指定期数完成活动的用户列表

### 管理员接口 (/admin/activity/*)

#### 活动配置管理
- **GET** `/admin/activity/list` - 获取活动配置列表
- **POST** `/admin/activity/create` - 创建新活动配置
- **PUT** `/admin/activity/update` - 更新活动配置
- **DELETE** `/admin/activity/delete/{id}` - 删除活动配置

#### 活动统计
- **GET** `/admin/activity/statistics/{period}` - 获取活动统计数据

## 业务逻辑

### 活动质押流程
1. 验证活动期数是否存在且进行中
2. 检查用户是否已参与该期数
3. 验证用户资产余额
4. 创建或更新用户活动记录
5. 调用原有质押接口
6. 记录用户操作日志

### 活动销毁流程
1. 验证活动期数是否存在且进行中
2. 检查用户是否已完成质押
3. 验证用户DOP余额
4. 更新用户活动记录
5. 调用原有销毁接口
6. 检查并更新完成状态
7. 记录用户操作日志

### 完成状态判定
用户同时完成质押和销毁操作后，`completed` 字段设置为1，表示该期数活动完成。

## 测试

### 单元测试
- `ActivityServiceTest` - 服务层测试
- `ActivityControllerTest` - 控制器集成测试

### 运行测试
```bash
# 运行所有活动相关测试
mvn test -Dtest="*Activity*"

# 运行特定测试类
mvn test -Dtest=ActivityServiceTest
mvn test -Dtest=ActivityControllerTest
```

## 部署说明

1. **数据库迁移**：在生产环境中创建新的数据库表
2. **配置验证**：确保活动配置数据正确
3. **测试验证**：在测试环境验证所有功能
4. **监控设置**：监控活动参与情况和系统性能

## 扩展性

系统设计支持以下扩展：
- 多种代币的活动支持
- 复杂的奖励机制
- 活动模板和批量创建
- 更详细的统计和报告功能

## 注意事项

1. **数据一致性**：活动操作与原有系统操作需要保持事务一致性
2. **并发控制**：用户重复提交的防护机制
3. **时间管理**：活动时间范围的严格控制
4. **资产安全**：质押和销毁操作的资产验证
5. **日志记录**：完整的操作日志用于审计和问题排查
