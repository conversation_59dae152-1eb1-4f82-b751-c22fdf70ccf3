package com.aic.app.model;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 活动配置表
 * @TableName activity_config
 */
@TableName(value = "activity_config")
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class ActivityConfig extends BaseEntity {
    
    /**
     * 期数
     */
    @Schema(description = "期数")
    private Integer period;
    
    /**
     * 该期固定质押数量
     */
    @Schema(description = "该期固定质押数量")
    private BigDecimal stakeAmount;
    
    /**
     * 该期固定销毁数量
     */
    @Schema(description = "该期固定销毁数量")
    private BigDecimal burnAmount;
    
    /**
     * 活动开始时间
     */
    @Schema(description = "活动开始时间")
    private Date startTime;
    
    /**
     * 活动结束时间
     */
    @Schema(description = "活动结束时间")
    private Date endTime;
    
    /**
     * 活动状态 0-未开始 1-进行中 2-已结束
     */
    @Schema(description = "活动状态 0-未开始 1-进行中 2-已结束")
    private Integer status;
    
    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Date updateTime;
    
    public ActivityConfig(Integer period, BigDecimal stakeAmount, BigDecimal burnAmount) {
        this.period = period;
        this.stakeAmount = stakeAmount;
        this.burnAmount = burnAmount;
        this.status = 1; // 默认进行中
    }
}
