package com.aic.app.model;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 用户活动参与记录表
 * @TableName user_activity_record
 */
@TableName(value = "user_activity_record")
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class UserActivityRecord extends BaseEntity {
    
    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private String userId;
    
    /**
     * 期数
     */
    @Schema(description = "期数")
    private Integer period;
    
    /**
     * 质押状态 0-未质押 1-已质押
     */
    @Schema(description = "质押状态 0-未质押 1-已质押")
    private Integer stakeStatus;
    
    /**
     * 质押时间
     */
    @Schema(description = "质押时间")
    private Date stakeTime;
    
    /**
     * 质押数量
     */
    @Schema(description = "质押数量")
    private BigDecimal stakeAmount;
    
    /**
     * 销毁状态 0-未销毁 1-已销毁
     */
    @Schema(description = "销毁状态 0-未销毁 1-已销毁")
    private Integer burnStatus;
    
    /**
     * 销毁时间
     */
    @Schema(description = "销毁时间")
    private Date burnTime;
    
    /**
     * 销毁数量
     */
    @Schema(description = "销毁数量")
    private BigDecimal burnAmount;
    
    /**
     * 是否完成该期活动 0-未完成 1-已完成
     */
    @Schema(description = "是否完成该期活动 0-未完成 1-已完成")
    private Integer completed;
    
    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Date updateTime;
    
    public UserActivityRecord(String userId, Integer period) {
        this.userId = userId;
        this.period = period;
        this.stakeStatus = 0;
        this.burnStatus = 0;
        this.completed = 0;
        this.stakeAmount = BigDecimal.ZERO;
        this.burnAmount = BigDecimal.ZERO;
    }
    
    /**
     * 检查是否已完成该期活动
     */
    public boolean isActivityCompleted() {
        return stakeStatus == 1 && burnStatus == 1;
    }
    
    /**
     * 更新完成状态
     */
    public void updateCompletedStatus() {
        this.completed = isActivityCompleted() ? 1 : 0;
    }
}
