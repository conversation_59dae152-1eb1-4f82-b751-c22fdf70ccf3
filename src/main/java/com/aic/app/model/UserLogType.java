package com.aic.app.model;

/**
 * 流水类型
 */
public enum UserLogType {

    CurrentBuy("购买活期", 0),

    RegularBuy("购买定期", 1),

    CurrentProfit("活期收益", 2),
    
    RegularProfit("定期收益", 3),

    InviteProfit("返佣奖励", 4),

    RedeemCurrent("赎回活期", 5),

    RedeemRegular("赎回定期", 6),

    RECEIVE("领取动态奖励", 7),

    RECEIVE_PROFIT("领取定期奖励", 8),

    BuyRegularReward("定期直推奖励", 10), 
    
    Transfer_Out("划出", 11),
    
    Transfer_In("划入", 12),
    
    Airdrop("空投", 13),
    
    AirdropFee("手续费分红", 15),

    Stake("质押", 32),
    StakeProfit("质押收益", 33),
    BuyNode("购买节点", 34),
    UnStake("解除质押", 35),
    WithdrawNodeReward("提现节点返佣", 36),
    WithdrawNodePool("提现节点池", 37),
    NodeDividend("节点分红", 38),
    StakeReward("质押返佣", 39),
    WeekDynamic("周分红", 40),
    StakeRelease("分期释放", 41),
    WithdrawWeek("提现周分红", 42),
    BuyNodeReward("购买节点返佣", 43),
    WithdrawFee("提现手续费", 44),
    StakeReward2("直推10%", 45),
    Burn("销毁", 46),
    ActivityStake("活动质押", 47),
    ActivityUnstake("活动解除质押", 48),
    ActivityBurn("活动销毁", 49);
    

    // 7,8,10,11,12
//    直推返佣、划转记录、动态提取、定期提取、赎回

    final String label;
    final int value;

    UserLogType(String label, int value) {
        this.label = label;
        this.value = value;
    }

    public int getValue() {
        return value;
    }
    
    public String getLabel() {
        return label;
    }
    
}
