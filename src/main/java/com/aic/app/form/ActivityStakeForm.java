package com.aic.app.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;

@Data
public class ActivityStakeForm {

    @Schema(description = "期数")
    @NotNull(message = "期数不能为空")
    @Positive(message = "期数必须大于0")
    private Integer period;

    @Schema(description = "资产ID")
    private String tokenId = "DOP"; // 默认为DOP
}
