package com.aic.app.mapper;

import com.aic.app.model.ActivityConfig;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 活动配置Mapper
 */
@Mapper
public interface ActivityConfigMapper extends BaseMapper<ActivityConfig> {
    
    /**
     * 根据期数查询活动配置
     */
    @Select("select * from activity_config where period = #{period}")
    ActivityConfig getByPeriod(@Param("period") Integer period);
    
    /**
     * 查询当前进行中的活动期数
     */
    @Select("select * from activity_config where status = 1 order by period desc limit 1")
    ActivityConfig getCurrentActivity();
}
