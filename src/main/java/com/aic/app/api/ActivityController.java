package com.aic.app.api;

import com.aic.app.form.ActivityBurnForm;
import com.aic.app.form.ActivityStakeForm;
import com.aic.app.model.ActivityConfig;
import com.aic.app.model.User;
import com.aic.app.model.UserActivityRecord;
import com.aic.app.service.IActivityService;
import com.aic.app.vo.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 活动控制器
 */
@Slf4j
@RestController
@RequestMapping("/activity")
@Tag(name = "活动管理", description = "活动质押、销毁相关接口")
public class ActivityController {
    
    @Autowired
    private IActivityService activityService;
    
    @PostMapping("/stake")
    @Operation(summary = "活动质押")
    public Result<Boolean> activityStake(@RequestAttribute("user") User user, 
                                        @Valid @RequestBody ActivityStakeForm form) {
        activityService.activityStake(user, form);
        return Result.success(true);
    }
    
    @PostMapping("/unstake")
    @Operation(summary = "活动解除质押")
    public Result<Boolean> activityUnstake(@RequestAttribute("user") User user, 
                                          @Valid @RequestBody ActivityStakeForm form) {
        activityService.activityUnstake(user, form);
        return Result.success(true);
    }
    
    @PostMapping("/burn")
    @Operation(summary = "活动销毁")
    public Result<Boolean> activityBurn(@RequestAttribute("user") User user, 
                                       @Valid @RequestBody ActivityBurnForm form) {
        activityService.activityBurn(user, form);
        return Result.success(true);
    }
    
    @GetMapping("/status/{period}")
    @Operation(summary = "查询用户某期活动状态")
    public Result<UserActivityRecord> getActivityStatus(@RequestAttribute("user") User user, 
                                                        @PathVariable Integer period) {
        UserActivityRecord record = activityService.getUserActivityStatus(user.getId(), period);
        return Result.success(record);
    }
    
    @GetMapping("/records")
    @Operation(summary = "查询用户所有活动记录")
    public Result<List<UserActivityRecord>> getUserActivityRecords(@RequestAttribute("user") User user) {
        List<UserActivityRecord> records = activityService.getUserActivityRecords(user.getId());
        return Result.success(records);
    }
    
    @GetMapping("/config/{period}")
    @Operation(summary = "查询活动配置")
    public Result<ActivityConfig> getActivityConfig(@PathVariable Integer period) {
        ActivityConfig config = activityService.getActivityConfigByPeriod(period);
        return Result.success(config);
    }
    
    @GetMapping("/current")
    @Operation(summary = "查询当前进行中的活动")
    public Result<ActivityConfig> getCurrentActivity() {
        ActivityConfig config = activityService.getCurrentActivity();
        return Result.success(config);
    }
}
