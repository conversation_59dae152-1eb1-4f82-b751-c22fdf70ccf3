package com.aic.app.service;

import com.aic.app.form.ActivityBurnForm;
import com.aic.app.form.ActivityStakeForm;
import com.aic.app.model.ActivityConfig;
import com.aic.app.model.User;
import com.aic.app.model.UserActivityRecord;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 活动服务接口
 */
public interface IActivityService extends IService<ActivityConfig> {
    
    /**
     * 活动质押
     * @param user 用户
     * @param form 质押表单
     */
    void activityStake(User user, ActivityStakeForm form);
    
    /**
     * 活动解除质押
     * @param user 用户
     * @param form 质押表单
     */
    void activityUnstake(User user, ActivityStakeForm form);
    
    /**
     * 活动销毁
     * @param user 用户
     * @param form 销毁表单
     */
    void activityBurn(User user, ActivityBurnForm form);
    
    /**
     * 获取用户活动状态
     * @param userId 用户ID
     * @param period 期数
     * @return 用户活动记录
     */
    UserActivityRecord getUserActivityStatus(String userId, Integer period);
    
    /**
     * 获取用户所有活动记录
     * @param userId 用户ID
     * @return 活动记录列表
     */
    List<UserActivityRecord> getUserActivityRecords(String userId);
    
    /**
     * 获取某期已完成的用户列表
     * @param period 期数
     * @return 已完成用户列表
     */
    List<UserActivityRecord> getCompletedUsersByPeriod(Integer period);
    
    /**
     * 根据期数获取活动配置
     * @param period 期数
     * @return 活动配置
     */
    ActivityConfig getActivityConfigByPeriod(Integer period);
    
    /**
     * 获取当前进行中的活动
     * @return 当前活动配置
     */
    ActivityConfig getCurrentActivity();
}
