package com.aic.app.service.impl;

import com.aic.app.exception.Errors;
import com.aic.app.form.ActivityBurnForm;
import com.aic.app.form.ActivityStakeForm;
import com.aic.app.form.StakeForm;
import com.aic.app.mapper.ActivityConfigMapper;
import com.aic.app.mapper.UserActivityRecordMapper;
import com.aic.app.model.ActivityConfig;
import com.aic.app.model.User;
import com.aic.app.model.UserActivityRecord;
import com.aic.app.service.IActivityService;
import com.aic.app.service.IStakeUserService;
import com.aic.app.service.IUserAssetService;
import com.aic.app.util.BizAssert;
import com.aic.app.util.UserLogType;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 活动服务实现类
 */
@Slf4j
@Service
public class ActivityServiceImpl extends ServiceImpl<ActivityConfigMapper, ActivityConfig> implements IActivityService {
    
    @Autowired
    private ActivityConfigMapper activityConfigMapper;
    
    @Autowired
    private UserActivityRecordMapper userActivityRecordMapper;
    
    @Autowired
    private IStakeUserService stakeUserService;
    
    @Autowired
    private IUserAssetService userAssetService;
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void activityStake(User user, ActivityStakeForm form) {
        log.info("活动质押开始 - 用户ID: {}, 期数: {}", user.getId(), form.getPeriod());
        
        // 1. 验证活动配置
        ActivityConfig activityConfig = getActivityConfigByPeriod(form.getPeriod());
        BizAssert.notNull(activityConfig, "活动配置不存在");
        BizAssert.isTrue(activityConfig.getStatus() == 1, "活动未开始或已结束");
        
        // 2. 获取或创建用户活动记录
        UserActivityRecord record = getUserActivityStatus(user.getId(), form.getPeriod());
        if (record == null) {
            record = new UserActivityRecord(user.getId(), form.getPeriod());
            userActivityRecordMapper.insert(record);
        }
        
        // 3. 检查是否已经质押
        BizAssert.isTrue(record.getStakeStatus() == 0, "该期已经质押，请先解除质押");
        
        // 4. 检查用户余额
        BigDecimal stakeAmount = activityConfig.getStakeAmount();
        BizAssert.isTrue(stakeAmount.compareTo(BigDecimal.ZERO) > 0, "质押数量必须大于0");
        
        // 5. 执行新的活动质押逻辑
        Date stakeTime = new Date();
        int updateResult = userActivityRecordMapper.updateStakeStatus(user.getId(), form.getPeriod(), stakeTime, stakeAmount);
        BizAssert.isTrue(updateResult == 1, "更新活动质押状态失败");
        
        // 6. 调用原有质押接口
        StakeForm stakeForm = new StakeForm();
        stakeForm.setTokenId(form.getTokenId());
        stakeForm.setAmount(stakeAmount);
        stakeUserService.stake(user, stakeForm);
        
        log.info("活动质押完成 - 用户ID: {}, 期数: {}, 质押数量: {}", user.getId(), form.getPeriod(), stakeAmount);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void activityUnstake(User user, ActivityStakeForm form) {
        log.info("活动解除质押开始 - 用户ID: {}, 期数: {}", user.getId(), form.getPeriod());
        
        // 1. 验证活动配置
        ActivityConfig activityConfig = getActivityConfigByPeriod(form.getPeriod());
        BizAssert.notNull(activityConfig, "活动配置不存在");
        
        // 2. 获取用户活动记录
        UserActivityRecord record = getUserActivityStatus(user.getId(), form.getPeriod());
        BizAssert.notNull(record, "未找到活动记录");
        BizAssert.isTrue(record.getStakeStatus() == 1, "该期未质押");
        
        // 3. 执行新的活动解除质押逻辑
        int updateResult = userActivityRecordMapper.updateUnstakeStatus(user.getId(), form.getPeriod());
        BizAssert.isTrue(updateResult == 1, "更新活动解除质押状态失败");
        
        // 4. 调用原有解除质押接口
        StakeForm stakeForm = new StakeForm();
        stakeForm.setTokenId(form.getTokenId());
        stakeForm.setAmount(record.getStakeAmount());
        stakeUserService.unStake(user, stakeForm);
        
        log.info("活动解除质押完成 - 用户ID: {}, 期数: {}, 解除数量: {}", user.getId(), form.getPeriod(), record.getStakeAmount());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void activityBurn(User user, ActivityBurnForm form) {
        log.info("活动销毁开始 - 用户ID: {}, 期数: {}", user.getId(), form.getPeriod());

        // 1. 验证活动配置
        ActivityConfig activityConfig = getActivityConfigByPeriod(form.getPeriod());
        BizAssert.notNull(activityConfig, "活动配置不存在");
        BizAssert.isTrue(activityConfig.getStatus() == 1, "活动未开始或已结束");

        // 2. 获取或创建用户活动记录
        UserActivityRecord record = getUserActivityStatus(user.getId(), form.getPeriod());
        if (record == null) {
            record = new UserActivityRecord(user.getId(), form.getPeriod());
            userActivityRecordMapper.insert(record);
        }

        // 3. 检查是否已经销毁
        BizAssert.isTrue(record.getBurnStatus() == 0, "该期已经销毁");

        // 4. 检查销毁数量
        BigDecimal burnAmount = activityConfig.getBurnAmount();
        BizAssert.isTrue(burnAmount.compareTo(BigDecimal.ZERO) > 0, "销毁数量必须大于0");

        // 5. 检查用户DOP余额
        BizAssert.isTrue(userAssetService.getAsset(user.getId(), "DOP").checkBalance(burnAmount),
                        () -> Errors.BALANCE_EXCEPTION);

        // 6. 执行销毁操作
        userAssetService.pay(user.getId(), "DOP", "DOP", burnAmount,
                           UserLogType.ActivityBurn.getValue(), "活动销毁-第" + form.getPeriod() + "期");

        // 7. 更新活动记录
        Date burnTime = new Date();
        int updateResult = userActivityRecordMapper.updateBurnStatus(user.getId(), form.getPeriod(), burnTime, burnAmount);
        BizAssert.isTrue(updateResult == 1, "更新活动销毁状态失败");

        log.info("活动销毁完成 - 用户ID: {}, 期数: {}, 销毁数量: {}", user.getId(), form.getPeriod(), burnAmount);
    }

    @Override
    public UserActivityRecord getUserActivityStatus(String userId, Integer period) {
        return userActivityRecordMapper.getByUserIdAndPeriod(userId, period);
    }

    @Override
    public List<UserActivityRecord> getUserActivityRecords(String userId) {
        return userActivityRecordMapper.getUserActivityRecords(userId);
    }

    @Override
    public List<UserActivityRecord> getCompletedUsersByPeriod(Integer period) {
        return userActivityRecordMapper.getCompletedUsersByPeriod(period);
    }

    @Override
    public ActivityConfig getActivityConfigByPeriod(Integer period) {
        return activityConfigMapper.getByPeriod(period);
    }

    @Override
    public ActivityConfig getCurrentActivity() {
        return activityConfigMapper.getCurrentActivity();
    }
