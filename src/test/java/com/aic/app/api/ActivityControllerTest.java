package com.aic.app.api;

import com.aic.app.form.ActivityBurnForm;
import com.aic.app.form.ActivityStakeForm;
import com.aic.app.model.ActivityConfig;
import com.aic.app.model.User;
import com.aic.app.model.UserActivityRecord;
import com.aic.app.service.IActivityService;
import com.aic.app.service.IUserService;
import com.aic.app.vo.Result;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 活动控制器集成测试
 */
@SpringBootTest
@ActiveProfiles("dev")
@Transactional
public class ActivityControllerTest {

    @Autowired
    private IActivityService activityService;

    @Autowired
    private IUserService userService;

    @Autowired
    private ObjectMapper objectMapper;

    private User testUser;

    @BeforeEach
    public void setUp() {
        // 创建测试用户（如果不存在）
        testUser = userService.getById("test_activity_user");
        if (testUser == null) {
            testUser = new User();
            testUser.setId("test_activity_user");
            testUser.setCode("TESTACT001");
            userService.save(testUser);
        }
    }

    @Test
    public void testGetCurrentActivity() {
        // 测试获取当前活动配置
        ActivityConfig currentActivity = activityService.getCurrentActivity();
        assertNotNull(currentActivity, "当前活动配置不应为空");
        assertEquals(1, currentActivity.getPeriod(), "当前活动期数应为1");
    }

    @Test
    public void testGetActivityByPeriod() {
        // 测试根据期数获取活动配置
        ActivityConfig config = activityService.getActivityConfigByPeriod(1);
        assertNotNull(config, "期数1的活动配置不应为空");
        assertEquals(1, config.getPeriod(), "期数应为1");

        // 测试不存在的期数
        ActivityConfig nonExistentConfig = activityService.getActivityConfigByPeriod(999);
        assertNull(nonExistentConfig, "不存在的期数应返回null");
    }

    @Test
    public void testGetUserActivityStatus() {
        // 测试获取用户活动状态
        UserActivityRecord record = activityService.getUserActivityStatus(testUser.getId(), 1);
        // 新用户应该没有活动记录
        assertNull(record, "新用户的活动记录应为null");
    }

    @Test
    public void testGetUserActivityRecords() {
        // 测试获取用户活动记录列表
        List<UserActivityRecord> records = activityService.getUserActivityRecords(testUser.getId());
        assertNotNull(records, "用户活动记录列表不应为null");
        assertTrue(records.isEmpty(), "新用户的活动记录列表应为空");
    }

    @Test
    public void testGetCompletedUsers() {
        // 测试获取指定期数的完成用户列表
        List<UserActivityRecord> completedUsers = activityService.getCompletedUsersByPeriod(1);
        assertNotNull(completedUsers, "完成用户列表不应为null");
    }

    @Test
    public void testServiceIntegration() {
        // 测试服务层集成
        ActivityConfig currentActivity = activityService.getCurrentActivity();
        assertNotNull(currentActivity, "当前活动配置不应为空");

        // 测试用户状态查询
        UserActivityRecord userStatus = activityService.getUserActivityStatus(testUser.getId(), currentActivity.getPeriod());
        // 新用户应该没有参与记录
        assertNull(userStatus, "新用户应该没有活动参与记录");

        // 测试完成用户列表
        List<UserActivityRecord> completedUsers = activityService.getCompletedUsersByPeriod(currentActivity.getPeriod());
        assertNotNull(completedUsers, "完成用户列表不应为null");
    }
}
