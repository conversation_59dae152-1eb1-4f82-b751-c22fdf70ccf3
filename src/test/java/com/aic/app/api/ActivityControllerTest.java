package com.aic.app.api;

import com.aic.app.form.ActivityBurnForm;
import com.aic.app.form.ActivityStakeForm;
import com.aic.app.model.User;
import com.aic.app.service.IUserService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.transaction.annotation.Transactional;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 活动控制器集成测试
 */
@SpringBootTest
@AutoConfigureMockMvc
//@ActiveProfiles("test")
@Transactional
public class ActivityControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private IUserService userService;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private org.springframework.data.redis.core.StringRedisTemplate stringRedisTemplate;

    private User testUser;
    private String testToken;

    @BeforeEach
    public void setUp() throws Exception {
        // 创建测试用户（如果不存在）
        testUser = userService.getById("test_activity_user");
        if (testUser == null) {
            testUser = new User();
            testUser.setId("test_activity_user");
            testUser.setCode("TESTACT001");
            testUser.setPid("parent_user"); // 设置邀请人，避免"请先绑定邀请人"错误
            userService.save(testUser);
        }

        // 创建用户资产记录（用于销毁测试）
        createUserAssetIfNotExists("test_activity_user", "DOP", new BigDecimal("100000"));

        // 模拟登录获取token
        login("TESTACT001");
    }

    /**
     * 模拟登录获取token
     */
    private void login(String code) throws Exception {
        // 方法1：直接设置Redis token（推荐用于测试）
        testToken = java.util.UUID.randomUUID().toString();
        String redisKey = "dop:user:token:" + testToken;
        stringRedisTemplate.opsForValue().set(redisKey, testUser.getId(), java.time.Duration.ofHours(2));

        // 方法2：也可以通过真实登录接口获取token（备用）
        /*
        MvcResult result = mockMvc.perform(post("/api/login")
                        .param("code", code))
                .andExpect(status().isOk())
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        try {
            com.fasterxml.jackson.databind.JsonNode jsonNode = objectMapper.readTree(responseContent);
            if (jsonNode.get("code").asInt() == 0 && jsonNode.has("data") && jsonNode.get("data").has("token")) {
                testToken = jsonNode.get("data").get("token").asText();
            }
        } catch (Exception e) {
            // 如果解析失败，使用方法1的token
        }
        */
    }

    /**
     * 打印JSON响应内容
     */
    private void printJson(String json) {
        System.out.println("Response: " + json);
    }

    @Test
    public void testGetCurrentActivity() throws Exception {
        // 测试获取当前活动配置
        MvcResult result = mockMvc.perform(get("/api/activity/current")
                        .header("Authorization", testToken))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.msg").value("success"))
                .andExpect(jsonPath("$.data").exists())
                .andReturn();

        printJson(result.getResponse().getContentAsString());
    }

    @Test
    public void testGetActivityByPeriod() throws Exception {
        // 测试根据期数获取活动配置
        MvcResult result = mockMvc.perform(get("/api/activity/config/1")
                        .header("Authorization", testToken))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.msg").value("success"))
                .andExpect(jsonPath("$.data").exists())
                .andReturn();

        printJson(result.getResponse().getContentAsString());

        // 测试不存在的期数
        mockMvc.perform(get("/api/activity/config/999")
                        .header("Authorization", testToken))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0));
    }

    @Test
    public void testGetUserActivityStatus() throws Exception {
        // 测试获取用户活动状态
        MvcResult result = mockMvc.perform(get("/api/activity/status/1")
                        .header("Authorization", testToken))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andReturn();

        printJson(result.getResponse().getContentAsString());
    }

    @Test
    public void testGetUserActivityRecords() throws Exception {
        // 测试获取用户活动记录列表
        MvcResult result = mockMvc.perform(get("/api/activity/records")
                        .header("Authorization", testToken))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.msg").value("success"))
                .andExpect(jsonPath("$.data").isArray())
                .andReturn();

        printJson(result.getResponse().getContentAsString());
    }

    // 注意：获取完成用户列表的接口在管理员控制器中，这里不测试

    @Test
    public void testActivityStakeEndpoint() throws Exception {
        // 测试活动质押接口
        ActivityStakeForm stakeForm = new ActivityStakeForm();
        stakeForm.setPeriod(1);
        stakeForm.setTokenId("DOP");

        MvcResult result = mockMvc.perform(post("/api/activity/stake")
                        .header("Authorization", testToken)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(stakeForm)))
                .andDo(print())
                .andExpect(status().isOk())
                .andReturn();

        printJson(result.getResponse().getContentAsString());
    }

    @Test
    public void testActivityBurnEndpoint() throws Exception {
        // 测试活动销毁接口
        ActivityBurnForm burnForm = new ActivityBurnForm();
        burnForm.setPeriod(1);

        MvcResult result = mockMvc.perform(post("/api/activity/burn")
                        .header("Authorization", testToken)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(burnForm)))
                .andDo(print())
                .andExpect(status().isOk())
                .andReturn();

        printJson(result.getResponse().getContentAsString());
    }

    @Test
    public void testValidationErrors() throws Exception {
        // 测试无效的期数参数
        mockMvc.perform(get("/api/activity/config/0")
                        .header("Authorization", testToken))
                .andDo(print())
                .andExpect(status().isOk());

        // 测试无效的期数参数
        mockMvc.perform(get("/api/activity/status/0")
                        .header("Authorization", testToken))
                .andDo(print())
                .andExpect(status().isOk());
    }
}
