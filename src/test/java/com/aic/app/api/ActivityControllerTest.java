package com.aic.app.api;

import com.aic.app.form.ActivityBurnForm;
import com.aic.app.form.ActivityStakeForm;
import com.aic.app.model.ActivityConfig;
import com.aic.app.model.User;
import com.aic.app.model.UserActivityRecord;
import com.aic.app.service.IActivityService;
import com.aic.app.service.IUserService;
import com.aic.app.vo.Result;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureTestMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 活动控制器集成测试
 */
@SpringBootTest
@AutoConfigureTestMvc
@ActiveProfiles("test")
@Transactional
public class ActivityControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private IActivityService activityService;

    @Autowired
    private IUserService userService;

    @Autowired
    private ObjectMapper objectMapper;

    private User testUser;

    @BeforeEach
    public void setUp() {
        // 创建测试用户（如果不存在）
        testUser = userService.getById("test_activity_user");
        if (testUser == null) {
            testUser = new User();
            testUser.setId("test_activity_user");
            testUser.setCode("TESTACT001");
            testUser.setNickname("测试活动用户");
            testUser.setEmail("<EMAIL>");
            userService.save(testUser);
        }
    }

    @Test
    public void testGetCurrentActivity() throws Exception {
        mockMvc.perform(get("/api/activity/current"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.period").value(1))
                .andExpect(jsonPath("$.data.stakeAmount").value("10000.00000000"))
                .andExpect(jsonPath("$.data.burnAmount").value("1000.00000000"))
                .andExpect(jsonPath("$.data.status").value(1));
    }

    @Test
    public void testGetActivityByPeriod() throws Exception {
        mockMvc.perform(get("/api/activity/period/1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.period").value(1));

        // 测试不存在的期数
        mockMvc.perform(get("/api/activity/period/999"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isEmpty());
    }

    @Test
    public void testGetUserActivityStatus() throws Exception {
        mockMvc.perform(get("/api/activity/user/status")
                .param("userId", testUser.getId())
                .param("period", "1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
    }

    @Test
    public void testGetUserActivityRecords() throws Exception {
        mockMvc.perform(get("/api/activity/user/records")
                .param("userId", testUser.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray());
    }

    @Test
    public void testGetCompletedUsers() throws Exception {
        mockMvc.perform(get("/api/activity/completed/1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray());
    }

    // 注意：以下测试需要真实的用户资产数据，在实际环境中可能需要mock
    // 这里提供测试框架，但不执行实际的质押和销毁操作以避免数据库状态变化

    /*
    @Test
    public void testActivityStakeEndpoint() throws Exception {
        ActivityStakeForm stakeForm = new ActivityStakeForm();
        stakeForm.setPeriod(1);
        stakeForm.setTokenId("DOP");

        mockMvc.perform(post("/api/activity/stake")
                .param("userId", testUser.getId())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(stakeForm)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
    }

    @Test
    public void testActivityBurnEndpoint() throws Exception {
        ActivityBurnForm burnForm = new ActivityBurnForm();
        burnForm.setPeriod(1);

        mockMvc.perform(post("/api/activity/burn")
                .param("userId", testUser.getId())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(burnForm)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
    }
    */

    @Test
    public void testValidationErrors() throws Exception {
        // 测试无效的期数参数
        mockMvc.perform(get("/api/activity/period/0"))
                .andExpect(status().isOk());

        // 测试空的用户ID
        mockMvc.perform(get("/api/activity/user/status")
                .param("userId", "")
                .param("period", "1"))
                .andExpect(status().isOk());
    }
}
