package com.aic.app.service;

import com.aic.app.form.ActivityBurnForm;
import com.aic.app.form.ActivityStakeForm;
import com.aic.app.model.ActivityConfig;
import com.aic.app.model.User;
import com.aic.app.model.UserActivityRecord;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 活动服务测试类
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class ActivityServiceTest {

    @Autowired
    private IActivityService activityService;

    @Autowired
    private IUserService userService;

    @Test
    public void testGetCurrentActivity() {
        // 测试获取当前活动配置
        ActivityConfig currentActivity = activityService.getCurrentActivity();
        assertNotNull(currentActivity, "当前活动配置不应为空");
        assertEquals(1, currentActivity.getPeriod(), "当前活动期数应为1");
        assertEquals(new BigDecimal("10000.00000000"), currentActivity.getStakeAmount(), "质押数量应为10000");
        assertEquals(new BigDecimal("1000.00000000"), currentActivity.getBurnAmount(), "销毁数量应为1000");
        assertEquals(1, currentActivity.getStatus(), "活动状态应为1（进行中）");
    }

    @Test
    public void testGetActivityConfigByPeriod() {
        // 测试根据期数获取活动配置
        ActivityConfig config = activityService.getActivityConfigByPeriod(1);
        assertNotNull(config, "期数1的活动配置不应为空");
        assertEquals(1, config.getPeriod(), "期数应为1");
        
        // 测试不存在的期数
        ActivityConfig nonExistentConfig = activityService.getActivityConfigByPeriod(999);
        assertNull(nonExistentConfig, "不存在的期数应返回null");
    }

    @Test
    public void testGetUserActivityStatus() {
        // 测试获取用户活动状态（不存在的记录）
        UserActivityRecord record = activityService.getUserActivityStatus("test_user_123", 1);
        assertNull(record, "不存在的用户活动记录应返回null");
    }

    @Test
    public void testGetUserActivityRecords() {
        // 测试获取用户活动记录列表
        List<UserActivityRecord> records = activityService.getUserActivityRecords("test_user_123");
        assertNotNull(records, "用户活动记录列表不应为null");
        assertTrue(records.isEmpty(), "新用户的活动记录列表应为空");
    }

    @Test
    public void testGetCompletedUsersByPeriod() {
        // 测试获取指定期数的完成用户列表
        List<UserActivityRecord> completedUsers = activityService.getCompletedUsersByPeriod(1);
        assertNotNull(completedUsers, "完成用户列表不应为null");
        assertTrue(completedUsers.isEmpty(), "期数1的完成用户列表应为空（因为还没有用户完成）");
    }

    // 注意：以下测试需要真实的用户数据和资产数据，在实际环境中可能需要mock
    // 这里提供测试框架，但不执行实际的质押操作以避免数据库状态变化

    /*
    @Test
    public void testActivityStakeFlow() {
        // 创建测试用户
        User testUser = new User();
        testUser.setId("test_user_activity");
        testUser.setCode("TEST001");
        
        // 创建活动质押表单
        ActivityStakeForm stakeForm = new ActivityStakeForm();
        stakeForm.setPeriod(1);
        stakeForm.setTokenId("DOP");
        
        // 测试活动质押（需要用户有足够余额）
        // activityService.activityStake(testUser, stakeForm);
        
        // 验证质押后的状态
        // UserActivityRecord record = activityService.getUserActivityStatus(testUser.getId(), 1);
        // assertNotNull(record, "质押后应有活动记录");
        // assertEquals(1, record.getStakeStatus(), "质押状态应为1");
    }

    @Test
    public void testActivityBurnFlow() {
        // 创建测试用户
        User testUser = new User();
        testUser.setId("test_user_burn");
        testUser.setCode("TEST002");
        
        // 创建活动销毁表单
        ActivityBurnForm burnForm = new ActivityBurnForm();
        burnForm.setPeriod(1);
        
        // 测试活动销毁（需要用户有足够DOP余额）
        // activityService.activityBurn(testUser, burnForm);
        
        // 验证销毁后的状态
        // UserActivityRecord record = activityService.getUserActivityStatus(testUser.getId(), 1);
        // assertNotNull(record, "销毁后应有活动记录");
        // assertEquals(1, record.getBurnStatus(), "销毁状态应为1");
    }
    */
}
